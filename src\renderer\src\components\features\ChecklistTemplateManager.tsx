import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Checkbox } from '../ui/checkbox'
import { Textarea } from '../ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '../ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { cn } from '../../lib/utils'
import { useLanguage } from '../../contexts/LanguageContext'
import { useTaskStore } from '../../store/taskStore'
import type { Checklist, ChecklistInstance } from '../../../../shared/types'

interface ChecklistTemplateManagerProps {
  areaId: string
  className?: string
}

interface ChecklistItem {
  id: string
  text: string
  completed: boolean
}

interface TemplateFormData {
  name: string
  items: ChecklistItem[]
}

export function ChecklistTemplateManager({ areaId, className }: ChecklistTemplateManagerProps) {
  const { t } = useLanguage()
  const { 
    checklists, 
    checklistInstances,
    addChecklist, 
    updateChecklist, 
    deleteChecklist,
    addChecklistInstance,
    updateChecklistInstance
  } = useTaskStore()
  
  const [isCreateTemplateOpen, setIsCreateTemplateOpen] = useState(false)
  const [editingTemplate, setEditingTemplate] = useState<Checklist | null>(null)
  const [templateForm, setTemplateForm] = useState<TemplateFormData>({
    name: '',
    items: []
  })

  // Filter checklists for this area
  const areaChecklists = checklists.filter(checklist => 
    (checklist as any).areaId === areaId
  )

  // Filter instances for this area's checklists
  const areaInstances = checklistInstances.filter(instance =>
    areaChecklists.some(checklist => checklist.id === instance.checklistId)
  )

  const handleCreateTemplate = () => {
    if (!templateForm.name.trim() || templateForm.items.length === 0) return

    const newChecklist: Omit<Checklist, 'id'> = {
      name: templateForm.name.trim(),
      template: templateForm.items.map(item => ({
        id: item.id,
        text: item.text,
        completed: false
      })),
      createdAt: new Date(),
      areaId: areaId as any // Type assertion for compatibility
    }

    addChecklist(newChecklist as Checklist)
    resetTemplateForm()
    setIsCreateTemplateOpen(false)
  }

  const handleEditTemplate = () => {
    if (!editingTemplate || !templateForm.name.trim() || templateForm.items.length === 0) return

    updateChecklist(editingTemplate.id, {
      name: templateForm.name.trim(),
      template: templateForm.items.map(item => ({
        id: item.id,
        text: item.text,
        completed: false
      }))
    })

    resetTemplateForm()
    setEditingTemplate(null)
  }

  const handleDeleteTemplate = (checklistId: string) => {
    deleteChecklist(checklistId)
  }

  const handleUseTemplate = (checklist: Checklist) => {
    const newInstance: Omit<ChecklistInstance, 'id'> = {
      checklistId: checklist.id,
      status: checklist.template,
      createdAt: new Date(),
      completedAt: undefined
    }

    addChecklistInstance(newInstance as ChecklistInstance)
  }

  const handleToggleInstanceItem = (instanceId: string, itemId: string) => {
    const instance = checklistInstances.find(i => i.id === instanceId)
    if (!instance) return

    const updatedStatus = Array.isArray(instance.status) 
      ? instance.status.map((item: any) => 
          item.id === itemId ? { ...item, completed: !item.completed } : item
        )
      : instance.status

    updateChecklistInstance(instanceId, { status: updatedStatus })

    // Check if all items are completed
    const allCompleted = Array.isArray(updatedStatus) && 
      updatedStatus.every((item: any) => item.completed)
    
    if (allCompleted && !instance.completedAt) {
      updateChecklistInstance(instanceId, { completedAt: new Date() })
    } else if (!allCompleted && instance.completedAt) {
      updateChecklistInstance(instanceId, { completedAt: undefined })
    }
  }

  const resetTemplateForm = () => {
    setTemplateForm({
      name: '',
      items: []
    })
  }

  const addTemplateItem = () => {
    const newItem: ChecklistItem = {
      id: `item-${Date.now()}`,
      text: '',
      completed: false
    }
    setTemplateForm(prev => ({
      ...prev,
      items: [...prev.items, newItem]
    }))
  }

  const updateTemplateItem = (itemId: string, text: string) => {
    setTemplateForm(prev => ({
      ...prev,
      items: prev.items.map(item => 
        item.id === itemId ? { ...item, text } : item
      )
    }))
  }

  const removeTemplateItem = (itemId: string) => {
    setTemplateForm(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId)
    }))
  }

  const openEditTemplate = (checklist: Checklist) => {
    setEditingTemplate(checklist)
    setTemplateForm({
      name: checklist.name,
      items: Array.isArray(checklist.template) 
        ? checklist.template.map((item: any) => ({
            id: item.id || `item-${Date.now()}-${Math.random()}`,
            text: item.text || '',
            completed: false
          }))
        : []
    })
  }

  const closeDialogs = () => {
    setIsCreateTemplateOpen(false)
    setEditingTemplate(null)
    resetTemplateForm()
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <svg className="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
              {t('pages.areas.detail.checklistTemplates') || '清单模板'}
            </CardTitle>
            <CardDescription>
              {t('pages.areas.detail.checklistTemplatesDescription') || '创建可复用的标准核查清单模板'}
            </CardDescription>
          </div>
          <Dialog open={isCreateTemplateOpen} onOpenChange={setIsCreateTemplateOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                {t('pages.areas.detail.createTemplate') || '创建模板'}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>{t('pages.areas.detail.createChecklistTemplate') || '创建清单模板'}</DialogTitle>
                <DialogDescription>
                  {t('pages.areas.detail.createTemplateDescription') || '创建一个可重复使用的清单模板'}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="template-name">{t('pages.areas.detail.templateName') || '模板名称'}</Label>
                  <Input
                    id="template-name"
                    value={templateForm.name}
                    onChange={(e) => setTemplateForm(prev => ({ ...prev, name: e.target.value }))}
                    placeholder={t('pages.areas.detail.templateNamePlaceholder') || '例如：每周家庭清洁清单'}
                  />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label>{t('pages.areas.detail.templateItems') || '清单项目'}</Label>
                    <Button type="button" variant="outline" size="sm" onClick={addTemplateItem}>
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      {t('pages.areas.detail.addItem') || '添加项目'}
                    </Button>
                  </div>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {templateForm.items.map((item, index) => (
                      <div key={item.id} className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground w-6">{index + 1}.</span>
                        <Input
                          value={item.text}
                          onChange={(e) => updateTemplateItem(item.id, e.target.value)}
                          placeholder={t('pages.areas.detail.itemPlaceholder') || '输入清单项目...'}
                          className="flex-1"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeTemplateItem(item.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </Button>
                      </div>
                    ))}
                    {templateForm.items.length === 0 && (
                      <p className="text-sm text-muted-foreground text-center py-4">
                        {t('pages.areas.detail.noItemsYet') || '尚未添加任何项目'}
                      </p>
                    )}
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={closeDialogs}>
                  {t('pages.areas.detail.cancel') || '取消'}
                </Button>
                <Button onClick={handleCreateTemplate}>
                  {t('pages.areas.detail.createTemplate') || '创建模板'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {/* Templates Section */}
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-3">{t('pages.areas.detail.templates') || '模板'}</h4>
            {areaChecklists.length === 0 ? (
              <div className="text-center py-6 text-muted-foreground">
                <div className="text-3xl mb-2">📋</div>
                <p className="text-sm">{t('pages.areas.detail.noTemplatesYet') || '尚未创建任何模板'}</p>
                <p className="text-xs mt-1">{t('pages.areas.detail.createFirstTemplate') || '创建第一个清单模板'}</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {areaChecklists.map((checklist) => (
                  <Card key={checklist.id} className="group hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-sm font-medium">{checklist.name}</CardTitle>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity">
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01" />
                              </svg>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openEditTemplate(checklist)}>
                              {t('pages.areas.detail.editTemplate') || '编辑模板'}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDeleteTemplate(checklist.id)}>
                              {t('pages.areas.detail.deleteTemplate') || '删除模板'}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-xs text-muted-foreground mb-2">
                        {Array.isArray(checklist.template) ? checklist.template.length : 0} {t('pages.areas.detail.items') || '项目'}
                      </p>
                      <Button 
                        size="sm" 
                        className="w-full" 
                        onClick={() => handleUseTemplate(checklist)}
                      >
                        {t('pages.areas.detail.useTemplate') || '使用此模板'}
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Active Instances Section */}
          {areaInstances.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-3">{t('pages.areas.detail.activeChecklists') || '活跃清单'}</h4>
              <div className="space-y-3">
                {areaInstances.map((instance) => {
                  const template = areaChecklists.find(c => c.id === instance.checklistId)
                  if (!template) return null

                  const items = Array.isArray(instance.status) ? instance.status : []
                  const completedCount = items.filter((item: any) => item.completed).length
                  const progress = items.length > 0 ? Math.round((completedCount / items.length) * 100) : 0

                  return (
                    <Card key={instance.id} className={cn(
                      "border-l-4",
                      instance.completedAt ? "border-l-green-500 bg-green-50/50" : "border-l-blue-500"
                    )}>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-sm font-medium">{template.name}</CardTitle>
                          <div className="text-xs text-muted-foreground">
                            {completedCount}/{items.length} ({progress}%)
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-2">
                          {items.map((item: any) => (
                            <div key={item.id} className="flex items-center gap-2">
                              <Checkbox
                                checked={item.completed}
                                onCheckedChange={() => handleToggleInstanceItem(instance.id, item.id)}
                              />
                              <span className={cn(
                                "text-sm flex-1",
                                item.completed && "line-through text-muted-foreground"
                              )}>
                                {item.text}
                              </span>
                            </div>
                          ))}
                        </div>
                        {instance.completedAt && (
                          <p className="text-xs text-green-600 mt-2">
                            ✅ {t('pages.areas.detail.completedOn') || '完成于'} {new Date(instance.completedAt).toLocaleDateString()}
                          </p>
                        )}
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>
          )}
        </div>
      </CardContent>

      {/* Edit Template Dialog */}
      <Dialog open={!!editingTemplate} onOpenChange={(open) => !open && closeDialogs()}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{t('pages.areas.detail.editTemplate') || '编辑模板'}</DialogTitle>
            <DialogDescription>
              {t('pages.areas.detail.editTemplateDescription') || '修改清单模板的名称和项目'}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-template-name">{t('pages.areas.detail.templateName') || '模板名称'}</Label>
              <Input
                id="edit-template-name"
                value={templateForm.name}
                onChange={(e) => setTemplateForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder={t('pages.areas.detail.templateNamePlaceholder') || '例如：每周家庭清洁清单'}
              />
            </div>
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label>{t('pages.areas.detail.templateItems') || '清单项目'}</Label>
                <Button type="button" variant="outline" size="sm" onClick={addTemplateItem}>
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  {t('pages.areas.detail.addItem') || '添加项目'}
                </Button>
              </div>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {templateForm.items.map((item, index) => (
                  <div key={item.id} className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground w-6">{index + 1}.</span>
                    <Input
                      value={item.text}
                      onChange={(e) => updateTemplateItem(item.id, e.target.value)}
                      placeholder={t('pages.areas.detail.itemPlaceholder') || '输入清单项目...'}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeTemplateItem(item.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={closeDialogs}>
              {t('pages.areas.detail.cancel') || '取消'}
            </Button>
            <Button onClick={handleEditTemplate}>
              {t('pages.areas.detail.saveChanges') || '保存更改'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}

export default ChecklistTemplateManager
