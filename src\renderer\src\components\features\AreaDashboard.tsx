import { useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { useLanguage } from '../../contexts/LanguageContext'

interface AreaDashboardProps {
  areaId: string
  areaName: string
  areaDescription?: string
  stats: {
    totalProjects: number
    completedProjects: number
    activeHabits: number
    checklistProgress: number
    completedChecklist: number
    totalChecklist: number
  }
  habits: any[]
  projects: any[]
  className?: string
}

export function AreaDashboard({
  areaId,
  areaName,
  areaDescription,
  stats,
  habits,
  projects,
  className
}: AreaDashboardProps) {
  const { t } = useLanguage()

  // 计算项目状态分布
  const projectStatusDistribution = useMemo(() => {
    const statusCounts = projects.reduce((acc, project) => {
      const status = project.status || 'Not Started'
      acc[status] = (acc[status] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return Object.entries(statusCounts).map(([status, count]) => ({
      status,
      count,
      percentage: projects.length > 0 ? Math.round((count / projects.length) * 100) : 0
    }))
  }, [projects])

  // 计算习惯完成率趋势（最近7天）
  const habitCompletionTrend = useMemo(() => {
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - i)
      return date.toISOString().split('T')[0]
    }).reverse()

    return last7Days.map(date => {
      const dayCompletions = habits.reduce((total, habit) => {
        const record = habit.records?.find((r: any) => r.date === date)
        return total + (record?.completed ? 1 : 0)
      }, 0)
      
      const completionRate = habits.length > 0 ? Math.round((dayCompletions / habits.length) * 100) : 0
      
      return {
        date,
        completionRate,
        dayName: new Date(date).toLocaleDateString('zh-CN', { weekday: 'short' })
      }
    })
  }, [habits])

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'In Progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'On Hold':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'Not Started':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // 计算整体健康度评分
  const healthScore = useMemo(() => {
    const projectScore = stats.totalProjects > 0 ? (stats.completedProjects / stats.totalProjects) * 30 : 0
    const habitScore = habits.length > 0 ? (habitCompletionTrend[habitCompletionTrend.length - 1]?.completionRate || 0) * 0.4 : 0
    const checklistScore = stats.checklistProgress * 0.3
    
    return Math.round(projectScore + habitScore + checklistScore)
  }, [stats, habits, habitCompletionTrend])

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <svg className="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          {areaName}
        </CardTitle>
        {areaDescription && (
          <CardDescription>
            {areaDescription}
          </CardDescription>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 整体健康度评分 */}
        <div className="text-center p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg">
          <div className="text-3xl font-bold text-purple-600 mb-1">{healthScore}</div>
          <div className="text-sm text-muted-foreground">领域健康度评分</div>
          <div className="mt-2">
            <Progress value={healthScore} className="h-2" />
          </div>
        </div>

        {/* 项目状态分布 */}
        <div>
          <h4 className="text-sm font-medium mb-3">项目状态分布</h4>
          <div className="space-y-2">
            {projectStatusDistribution.map(({ status, count, percentage }) => (
              <div key={status} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={getStatusColor(status)}>
                    {status}
                  </Badge>
                  <span className="text-sm text-muted-foreground">{count} 个项目</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                  <span className="text-xs text-muted-foreground w-8">{percentage}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 习惯完成趋势 */}
        {habits.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-3">习惯完成趋势（最近7天）</h4>
            <div className="flex items-end justify-between h-20 gap-1">
              {habitCompletionTrend.map(({ date, completionRate, dayName }) => (
                <div key={date} className="flex flex-col items-center flex-1">
                  <div 
                    className="w-full bg-blue-500 rounded-t transition-all duration-300 min-h-[4px]"
                    style={{ height: `${Math.max(completionRate * 0.8, 4)}px` }}
                    title={`${dayName}: ${completionRate}%`}
                  />
                  <div className="text-xs text-muted-foreground mt-1">{dayName}</div>
                </div>
              ))}
            </div>
            <div className="text-xs text-muted-foreground text-center mt-2">
              平均完成率: {Math.round(habitCompletionTrend.reduce((sum, day) => sum + day.completionRate, 0) / habitCompletionTrend.length)}%
            </div>
          </div>
        )}

        {/* 关键指标概览 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-lg font-semibold text-green-600">{stats.completedProjects}</div>
            <div className="text-xs text-muted-foreground">已完成项目</div>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-lg font-semibold text-blue-600">{stats.activeHabits}</div>
            <div className="text-xs text-muted-foreground">活跃习惯</div>
          </div>
        </div>

        {/* 清单完成进度 */}
        {stats.totalChecklist > 0 && (
          <div>
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium">清单完成进度</h4>
              <span className="text-sm text-muted-foreground">
                {stats.completedChecklist}/{stats.totalChecklist}
              </span>
            </div>
            <Progress value={stats.checklistProgress} className="h-2" />
            <div className="text-xs text-muted-foreground text-center mt-1">
              {stats.checklistProgress}% 完成
            </div>
          </div>
        )}

        {/* 快速洞察 */}
        <div className="p-3 bg-amber-50 rounded-lg">
          <h4 className="text-sm font-medium text-amber-800 mb-2">💡 快速洞察</h4>
          <div className="text-xs text-amber-700 space-y-1">
            {stats.completedProjects === stats.totalProjects && stats.totalProjects > 0 && (
              <div>🎉 所有项目已完成！考虑设定新的目标。</div>
            )}
            {habits.length > 0 && habitCompletionTrend[habitCompletionTrend.length - 1]?.completionRate === 100 && (
              <div>⭐ 昨日习惯全部完成！保持这个节奏。</div>
            )}
            {stats.checklistProgress === 100 && stats.totalChecklist > 0 && (
              <div>✅ 所有清单项目已完成！</div>
            )}
            {healthScore >= 80 && (
              <div>🚀 领域表现优秀！继续保持。</div>
            )}
            {healthScore < 50 && (
              <div>📈 建议关注习惯养成和项目推进。</div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default AreaDashboard
